// Role-based access control middleware
export default defineNuxtRouteMiddleware(async (to, _from) => {
  try {
    // Get current user
    const response = await $fetch('/api/auth/me', {
      headers: {
        'Authorization': `Bearer ${useCookie('accessToken').value || ''}`
      }
    })

    if (!response.success || !response.user) {
      return navigateTo('/login')
    }

    const user = response.user
    const userRole = user.role

    // Define role-based route access rules
    const roleRoutes = {
      teacher: [
        '/teacher',
        '/teacher/dashboard',
        '/teacher/projects',
        '/teacher/tasks',
        '/teacher/reviews',
        '/teacher/students'
      ],
      student: [
        '/student',
        '/student/dashboard',
        '/student/projects',
        '/student/tasks',
        '/student/submissions',
        '/student/profile'
      ],
      admin: [
        '/admin',
        '/admin/dashboard',
        '/admin/users',
        '/admin/projects',
        '/admin/system'
      ]
    }

    // Check if current route is allowed for user's role
    const allowedRoutes = roleRoutes[userRole as keyof typeof roleRoutes] || []
    const isRouteAllowed = allowedRoutes.some(route => to.path.startsWith(route))

    // Allow access to common routes
    const commonRoutes = ['/profile', '/settings', '/logout']
    const isCommonRoute = commonRoutes.some(route => to.path.startsWith(route))

    if (!isRouteAllowed && !isCommonRoute) {
      // Redirect to appropriate dashboard based on role
      if (userRole === 'teacher') {
        return navigateTo('/teacher/dashboard')
      } else if (userRole === 'student') {
        return navigateTo('/student/dashboard')
      } else if (userRole === 'admin') {
        return navigateTo('/admin/dashboard')
      } else {
        return navigateTo('/login')
      }
    }
  } catch (error) {
    console.error('Role check failed:', error)
    return navigateTo('/login')
  }
})
