// Server-side authentication utilities
import type { H3Event } from 'h3'
import { UserRepository } from '../repositories/user'
import { verifyToken, extractTokenFromHeader } from './jwt'
import type { User } from '../types'

/**
 * Get authenticated user from request
 */
export async function getAuthenticatedUser(event: H3Event): Promise<User | null> {
  try {
    // Extract token from Authorization header
    const authHeader = getHeader(event, 'authorization')
    const token = extractTokenFromHeader(authHeader)

    if (!token) {
      return null
    }

    // Verify token
    const payload = verifyToken(token)
    if (!payload) {
      return null
    }

    // Get user from database
    const userRepository = new UserRepository()
    const user = await userRepository.findById(payload.userId)

    return user
  } catch (error) {
    console.error('Failed to get authenticated user:', error)
    return null
  }
}

/**
 * Require authentication for API endpoint
 */
export async function requireAuth(event: H3Event): Promise<User> {
  const user = await getAuthenticatedUser(event)
  
  if (!user) {
    setResponseStatus(event, 401)
    throw createError({
      statusCode: 401,
      statusMessage: 'Authentication required'
    })
  }

  return user
}

/**
 * Require specific role for API endpoint
 */
export async function requireRole(event: H3Event, requiredRole: 'teacher' | 'student' | 'admin'): Promise<User> {
  const user = await requireAuth(event)

  if (user.role !== requiredRole) {
    setResponseStatus(event, 403)
    throw createError({
      statusCode: 403,
      statusMessage: 'Insufficient permissions'
    })
  }

  return user
}

/**
 * Require admin role for API endpoint
 */
export async function requireAdmin(event: H3Event): Promise<User> {
  return requireRole(event, 'admin')
}

/**
 * Require teacher role for API endpoint
 */
export async function requireTeacher(event: H3Event): Promise<User> {
  return requireRole(event, 'teacher')
}

/**
 * Require student role for API endpoint
 */
export async function requireStudent(event: H3Event): Promise<User> {
  return requireRole(event, 'student')
}

/**
 * Check if user can access resource
 */
export async function canAccessResource(
  event: H3Event,
  resourceOwnerId: number,
  allowedRoles: ('teacher' | 'student' | 'admin')[] = []
): Promise<boolean> {
  try {
    const user = await getAuthenticatedUser(event)

    if (!user) {
      return false
    }

    // Admin can access all resources
    if (user.role === 'admin') {
      return true
    }

    // User can access their own resources
    if (user.user_id === resourceOwnerId) {
      return true
    }

    // Check if user's role is in allowed roles
    if (allowedRoles.includes(user.role)) {
      return true
    }

    // Teachers can access student resources
    if (user.role === 'teacher') {
      return true
    }

    return false
  } catch (error) {
    console.error('Failed to check resource access:', error)
    return false
  }
}
