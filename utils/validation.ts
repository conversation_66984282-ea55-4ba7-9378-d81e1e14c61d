// Validation utilities using Zod
import { z } from 'zod'

// User validation schemas
export const loginSchema = z.object({
  username: z.string().min(1, 'Username is required'),
  password: z.string().min(1, 'Password is required')
})

export const createUserSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  email: z.string().email('Invalid email format'),
  full_name: z.string().optional(),
  role: z.enum(['teacher', 'student', 'admin'])
})

export const updateUserSchema = z.object({
  username: z.string().min(3, 'Username must be at least 3 characters').optional(),
  password: z.string().min(6, 'Password must be at least 6 characters').optional(),
  email: z.string().email('Invalid email format').optional(),
  full_name: z.string().optional()
})

// Project validation schemas
export const createProjectSchema = z.object({
  project_name: z.string().min(1, 'Project name is required'),
  description: z.string().optional(),
  student_ids: z.array(z.number()).min(1, 'At least one student must be assigned')
})

// Task validation schemas
export const createTaskSchema = z.object({
  project_id: z.number(),
  title: z.string().min(1, 'Task title is required'),
  description: z.string().optional(),
  due_date: z.string().optional()
})

// Submission validation schemas
export const createSubmissionSchema = z.object({
  task_id: z.number(),
  progress_description: z.string().min(1, 'Progress description is required'),
  git_commit_hash: z.string().min(1, 'Git commit hash is required')
})

// Feedback validation schemas
export const createFeedbackSchema = z.object({
  submission_id: z.number(),
  comment: z.string().min(1, 'Feedback comment is required'),
  status: z.enum(['completed', 'needs_revision'])
})

// Validation helper functions
export function validateLogin(data: any) {
  try {
    const result = loginSchema.parse(data)
    return { success: true, data: result }
  } catch (error: any) {
    return { success: false, errors: error.errors }
  }
}

export function validateCreateUser(data: any) {
  try {
    const result = createUserSchema.parse(data)
    return { success: true, data: result }
  } catch (error: any) {
    return { success: false, errors: error.errors }
  }
}

export function validateUpdateUser(data: any) {
  try {
    const result = updateUserSchema.parse(data)
    return { success: true, data: result }
  } catch (error: any) {
    return { success: false, errors: error.errors }
  }
}

export function validateCreateProject(data: any) {
  try {
    const result = createProjectSchema.parse(data)
    return { success: true, data: result }
  } catch (error: any) {
    return { success: false, errors: error.errors }
  }
}

export function validateCreateTask(data: any) {
  try {
    const result = createTaskSchema.parse(data)
    return { success: true, data: result }
  } catch (error: any) {
    return { success: false, errors: error.errors }
  }
}

export function validateCreateSubmission(data: any) {
  try {
    const result = createSubmissionSchema.parse(data)
    return { success: true, data: result }
  } catch (error: any) {
    return { success: false, errors: error.errors }
  }
}

export function validateCreateFeedback(data: any) {
  try {
    const result = createFeedbackSchema.parse(data)
    return { success: true, data: result }
  } catch (error: any) {
    return { success: false, errors: error.errors }
  }
}