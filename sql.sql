-- Active: 1752723075833@@127.0.0.1@3306@projectManageSystem
-- ------------------------------------------------------------
--  项目进度管理系统数据库脚本
--  目标数据库: MySQL 5.7+
--  方案: 集成 Git 仓库
-- ------------------------------------------------------------

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
--  表结构: users (用户表)
--  说明: 存储教师和学生的基本信息。
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `user_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '用户ID (主键)',
  `username` VARCHAR(50) NOT NULL COMMENT '用户名, 用于登录',
  `password_hash` VARCHAR(255) NOT NULL COMMENT '哈希处理后的密码',
  `email` VARCHAR(100) NOT NULL COMMENT '电子邮箱, 用于通知',
  `full_name` VARCHAR(50) NULL DEFAULT NULL COMMENT '真实姓名',
  `role` ENUM('teacher', 'student') NOT NULL COMMENT '用户角色 (教师/学生)',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '账号创建时间',
  PRIMARY KEY (`user_id`),
  UNIQUE KEY `uk_username` (`username`),
  UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 为users表的role字段添加admin值
ALTER TABLE `users` 
MODIFY COLUMN `role` ENUM('teacher', 'student', 'admin') NOT NULL COMMENT '用户角色 (教师/学生/管理员)';
-- ----------------------------
--  表结构: projects (项目/课程表)
--  说明: 由教师创建，用于管理一个具体的项目或课程。
-- ----------------------------
DROP TABLE IF EXISTS `projects`;
CREATE TABLE `projects` (
  `project_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '项目ID (主键)',
  `project_name` VARCHAR(100) NOT NULL COMMENT '项目或课程名称',
  `description` TEXT NULL COMMENT '项目详细描述',
  `teacher_id` INT UNSIGNED NOT NULL COMMENT '指导老师ID (外键)',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`project_id`),
  CONSTRAINT `fk_projects_teacher` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目/课程表';

-- ----------------------------
--  表结构: student_projects (学生-项目关联表)
--  说明: 描述学生参与了哪些项目，并在此处指定学生用于该项目的代码仓库地址。
-- ----------------------------
DROP TABLE IF EXISTS `student_projects`;
CREATE TABLE `student_projects` (
  `student_id` INT UNSIGNED NOT NULL COMMENT '学生ID (外键)',
  `project_id` INT UNSIGNED NOT NULL COMMENT '项目ID (外键)',
  `git_repo_url` VARCHAR(255) NULL DEFAULT NULL COMMENT '学生在此项目中的代码仓库地址 (如: https://github.com/user/repo.git)',
  PRIMARY KEY (`student_id`, `project_id`),
  CONSTRAINT `fk_student_projects_student` FOREIGN KEY (`student_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_student_projects_project` FOREIGN KEY (`project_id`) REFERENCES `projects` (`project_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学生-项目关联表';

-- ----------------------------
--  表结构: tasks (任务表)
--  说明: 教师在项目中为学生分配的具体任务或学习目标。
-- ----------------------------
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks` (
  `task_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '任务ID (主键)',
  `project_id` INT UNSIGNED NOT NULL COMMENT '所属项目ID (外键)',
  `title` VARCHAR(150) NOT NULL COMMENT '任务标题',
  `description` TEXT NULL COMMENT '任务详细描述',
  `due_date` DATETIME NULL DEFAULT NULL COMMENT '截止日期',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '任务发布时间',
  PRIMARY KEY (`task_id`),
  CONSTRAINT `fk_tasks_project` FOREIGN KEY (`project_id`) REFERENCES `projects` (`project_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- ----------------------------
--  表结构: submissions (进度提交表)
--  说明: 核心表，学生针对每个任务提交进度，包含描述和关键的 Commit Hash。
-- ----------------------------
DROP TABLE IF EXISTS `submissions`;
CREATE TABLE `submissions` (
  `submission_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '提交记录ID (主键)',
  `task_id` INT UNSIGNED NOT NULL COMMENT '关联的任务ID (外键)',
  `student_id` INT UNSIGNED NOT NULL COMMENT '提交的学生ID (外键)',
  `progress_description` TEXT NOT NULL COMMENT '学生填写的进度文字描述',
  `git_commit_hash` VARCHAR(40) NULL DEFAULT NULL COMMENT '关联的Git Commit Hash (SHA-1)',
  `submission_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '提交时间',
  `status` ENUM('in_progress', 'submitted', 'completed', 'needs_revision') NOT NULL DEFAULT 'in_progress' COMMENT '提交状态',
  PRIMARY KEY (`submission_id`),
  CONSTRAINT `fk_submissions_task` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`task_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_submissions_student` FOREIGN KEY (`student_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='进度提交表';

-- ----------------------------
--  表结构: feedback (教师反馈表)
--  说明: 教师针对学生的某次提交给出具体的反馈意见。
-- ----------------------------
DROP TABLE IF EXISTS `feedback`;
CREATE TABLE `feedback` (
  `feedback_id` INT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT '反馈ID (主键)',
  `submission_id` INT UNSIGNED NOT NULL COMMENT '关联的提交记录ID (外键)',
  `teacher_id` INT UNSIGNED NOT NULL COMMENT '给出反馈的老师ID (外键)',
  `comment` TEXT NOT NULL COMMENT '反馈内容',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '反馈创建时间',
  PRIMARY KEY (`feedback_id`),
  CONSTRAINT `fk_feedback_submission` FOREIGN KEY (`submission_id`) REFERENCES `submissions` (`submission_id`) ON DELETE CASCADE ON UPDATE CASCADE,
  CONSTRAINT `fk_feedback_teacher` FOREIGN KEY (`teacher_id`) REFERENCES `users` (`user_id`) ON DELETE CASCADE ON UPDATE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='教师反馈表';

SET FOREIGN_KEY_CHECKS = 1;