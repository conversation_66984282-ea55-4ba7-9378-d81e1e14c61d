// Authentication store using Pinia
import { defineStore } from 'pinia'
import type { SafeUser } from '~/types'

export const useAuthStore = defineStore('auth', () => {
  // State
  const user = ref<SafeUser | null>(null)
  const token = ref<string | null>(null)
  
  // Getters
  const isAuthenticated = computed(() => !!user.value)
  const isTeacher = computed(() => user.value?.role === 'teacher')
  const isStudent = computed(() => user.value?.role === 'student')
  const isAdmin = computed(() => user.value?.role === 'admin')
  
  // Actions
  const login = async (credentials: { username: string; password: string }) => {
    try {
      const response = await $fetch('/api/auth/login', {
        method: 'POST',
        body: credentials
      }) as any

      if (response.success && response.user && response.token) {
        // Set user data
        user.value = response.user

        // Set access token in cookie
        const accessTokenCookie = useCookie('accessToken', {
          maxAge: 24 * 60 * 60, // 24 hours
          secure: process.env.NODE_ENV === 'production',
          sameSite: 'strict'
        })
        accessTokenCookie.value = response.token

        return { success: true, user: response.user }
      } else {
        return { success: false, error: response.error || { message: '登录失败' } }
      }
    } catch (error: any) {
      console.error('Login error:', error)
      return {
        success: false,
        error: {
          message: error.data?.error?.message || '登录失败，请稍后重试'
        }
      }
    }
  }

  const logout = async () => {
    try {
      // Call logout API
      await $fetch('/api/auth/logout', {
        method: 'POST'
      })
    } catch (error) {
      console.error('Logout API error:', error)
    } finally {
      // Clear user data and tokens regardless of API call result
      user.value = null

      // Clear access token cookie
      const accessTokenCookie = useCookie('accessToken')
      accessTokenCookie.value = null

      // Navigate to login page
      await navigateTo('/login')
    }
  }

  const checkAuth = async () => {
    try {
      const accessTokenCookie = useCookie('accessToken')

      if (!accessTokenCookie.value) {
        return false
      }

      const response = await $fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${accessTokenCookie.value}`
        }
      }) as any

      if (response.success && response.user) {
        user.value = response.user
        return true
      } else {
        // Try to refresh token
        try {
          const refreshResponse = await $fetch('/api/auth/refresh', {
            method: 'POST'
          }) as any

          if (refreshResponse.success && refreshResponse.user) {
            user.value = refreshResponse.user
            accessTokenCookie.value = refreshResponse.token
            return true
          }
        } catch (refreshError) {
          console.error('Token refresh failed:', refreshError)
        }

        // Clear invalid tokens
        user.value = null
        accessTokenCookie.value = null
        return false
      }
    } catch (error) {
      console.error('Auth check failed:', error)
      user.value = null
      const accessTokenCookie = useCookie('accessToken')
      accessTokenCookie.value = null
      return false
    }
  }
  
  return {
    // State
    user: readonly(user),
    token: readonly(token),

    // Getters
    isAuthenticated,
    isTeacher,
    isStudent,
    isAdmin,

    // Actions
    login,
    logout,
    checkAuth
  }
})