<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 自动导航栏 -->
    <AppNavigation />

    <!-- 返回按钮 -->
    <div class="mb-6">
      <UButton
        variant="ghost"
        @click="navigateTo('/student/projects')"
        class="mb-4"
      >
        <UIcon name="i-heroicons-arrow-left" class="mr-2" />
        返回项目列表
      </UButton>
    </div>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl" />
      <span class="ml-2">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <UAlert
      v-else-if="error"
      icon="i-heroicons-exclamation-triangle"
      color="red"
      variant="soft"
      title="加载失败"
      :description="error.message || '获取项目详情失败'"
      class="mb-6"
    />

    <!-- 项目详情 -->
    <div v-else-if="projectData">
      <!-- 项目基本信息 -->
      <UCard class="mb-6">
        <template #header>
          <div class="flex items-center justify-between">
            <h1 class="text-2xl font-bold text-gray-900">{{ projectData.project_name }}</h1>
            <UButton
              @click="showRepoModal = true"
              :color="projectData.git_repo_url ? 'green' : 'orange'"
              variant="soft"
              size="sm"
            >
              <UIcon name="i-heroicons-code-bracket" class="mr-2" />
              {{ projectData.git_repo_url ? '已配置Git仓库' : '配置Git仓库' }}
            </UButton>
          </div>
        </template>

        <div class="space-y-4">
          <div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">项目描述</h3>
            <p class="text-gray-600">{{ projectData.description || '暂无描述' }}</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 class="font-medium text-gray-900 mb-1">指导老师</h4>
              <p class="text-gray-600">{{ projectData.teacher_name }}</p>
            </div>
            <div>
              <h4 class="font-medium text-gray-900 mb-1">联系邮箱</h4>
              <p class="text-gray-600">{{ projectData.teacher_email }}</p>
            </div>
            <div>
              <h4 class="font-medium text-gray-900 mb-1">创建时间</h4>
              <p class="text-gray-600">{{ formatDate(projectData.created_at) }}</p>
            </div>
            <div>
              <h4 class="font-medium text-gray-900 mb-1">Git仓库</h4>
              <div v-if="projectData.git_repo_url" class="flex items-center">
                <a
                  :href="projectData.git_repo_url"
                  target="_blank"
                  class="text-blue-600 hover:text-blue-800 underline truncate max-w-xs"
                >
                  {{ projectData.git_repo_url }}
                </a>
                <UIcon name="i-heroicons-arrow-top-right-on-square" class="ml-1 text-gray-400" />
              </div>
              <p v-else class="text-orange-600">未配置</p>
            </div>
          </div>
        </div>
      </UCard>

      <!-- 项目任务 -->
      <UCard>
        <template #header>
          <div class="flex items-center justify-between">
            <h2 class="text-xl font-semibold text-gray-900">项目任务</h2>
            <UBadge variant="soft" size="lg">
              {{ projectData.tasks.length }} 个任务
            </UBadge>
          </div>
        </template>

        <div v-if="projectData.tasks.length === 0" class="text-center py-8">
          <UIcon name="i-heroicons-clipboard-document-list" class="text-6xl text-gray-300 mx-auto mb-4" />
          <h3 class="text-lg font-medium text-gray-900 mb-2">暂无任务</h3>
          <p class="text-gray-500">该项目还没有发布任何任务</p>
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="task in projectData.tasks"
            :key="task.task_id"
            class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
            @click="navigateTo(`/student/tasks/${task.task_id}`)"
          >
            <div class="flex items-center justify-between mb-2">
              <h3 class="text-lg font-medium text-gray-900">{{ task.title }}</h3>
              <UBadge
                :color="getStatusColor(task.submission_status)"
                variant="soft"
              >
                {{ getStatusText(task.submission_status) }}
              </UBadge>
            </div>

            <p v-if="task.description" class="text-gray-600 text-sm mb-3 line-clamp-2">
              {{ task.description }}
            </p>

            <div class="flex items-center justify-between text-sm text-gray-500">
              <div class="flex items-center space-x-4">
                <span v-if="task.due_date" class="flex items-center">
                  <UIcon name="i-heroicons-calendar" class="mr-1" />
                  截止：{{ formatDate(task.due_date) }}
                </span>
                <span class="flex items-center">
                  <UIcon name="i-heroicons-clock" class="mr-1" />
                  创建：{{ formatDate(task.created_at) }}
                </span>
              </div>
              <UIcon name="i-heroicons-arrow-right" class="text-gray-400" />
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Git仓库配置模态框 -->
    <UModal v-model="showRepoModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">配置Git仓库</h3>
        </template>

        <UForm
          :schema="repoSchema"
          :state="repoForm"
          @submit="updateGitRepo"
          class="space-y-4"
        >
          <UFormGroup label="Git仓库URL" name="git_repo_url" required>
            <UInput
              v-model="repoForm.git_repo_url"
              placeholder="https://github.com/username/repository.git"
              type="url"
            />
          </UFormGroup>

          <div class="flex justify-end space-x-3">
            <UButton
              variant="ghost"
              @click="showRepoModal = false"
              :disabled="repoLoading"
            >
              取消
            </UButton>
            <UButton
              type="submit"
              :loading="repoLoading"
            >
              保存
            </UButton>
          </div>
        </UForm>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
import { z } from 'zod'

// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 获取项目ID
const route = useRoute()
const projectId = parseInt(route.params.id as string)

// 获取项目详情数据
const { data: projectResponse, pending, error, refresh } = await useAuthFetch(`/api/student/projects/${projectId}`, {
  server: false,
  default: () => ({
    success: false,
    data: null
  })
})

// 提取项目数据
const projectData = computed(() => projectResponse.value?.data)

// Git仓库配置相关
const showRepoModal = ref(false)
const repoLoading = ref(false)

// Git仓库表单验证
const repoSchema = z.object({
  git_repo_url: z.string().url('请输入有效的Git仓库URL').min(1, 'Git仓库URL不能为空')
})

// Git仓库表单数据
const repoForm = reactive({
  git_repo_url: ''
})

// 监听项目数据变化，更新表单
watch(projectData, (newData) => {
  if (newData?.git_repo_url) {
    repoForm.git_repo_url = newData.git_repo_url
  }
}, { immediate: true })

// 更新Git仓库
const updateGitRepo = async () => {
  try {
    repoLoading.value = true

    // 使用useAuthFetch来确保认证头正确传递
    const response = await $fetch(`/api/student/projects/${projectId}/repo`, {
      method: 'PUT',
      body: {
        git_repo_url: repoForm.git_repo_url
      },
      headers: {
        'Authorization': `Bearer ${useCookie('accessToken').value}`
      }
    })

    if (response.success) {
      showRepoModal.value = false
      await refresh()

      // 显示成功提示
      const toast = useToast()
      toast.add({
        title: '成功',
        description: 'Git仓库地址已更新',
        color: 'green'
      })
    }
  } catch (error: any) {
    console.error('Update repo error:', error)

    const toast = useToast()
    toast.add({
      title: '更新失败',
      description: error.data?.error?.message || '更新Git仓库地址失败',
      color: 'red'
    })
  } finally {
    repoLoading.value = false
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// 获取状态颜色
const getStatusColor = (status: string) => {
  switch (status) {
    case 'not_submitted':
      return 'gray'
    case 'in_progress':
      return 'blue'
    case 'submitted':
      return 'yellow'
    case 'completed':
      return 'green'
    case 'needs_revision':
      return 'orange'
    default:
      return 'gray'
  }
}

// 获取状态文本
const getStatusText = (status: string) => {
  switch (status) {
    case 'not_submitted':
      return '未提交'
    case 'in_progress':
      return '进行中'
    case 'submitted':
      return '已提交'
    case 'completed':
      return '已完成'
    case 'needs_revision':
      return '需要修改'
    default:
      return '未知状态'
  }
}

// Set page title
useHead({
  title: computed(() => projectData.value ? `${projectData.value.project_name} - 项目详情` : '项目详情'),
  meta: [
    {
      name: 'description',
      content: computed(() => projectData.value?.description || '项目详情页面')
    }
  ]
})
</script>
