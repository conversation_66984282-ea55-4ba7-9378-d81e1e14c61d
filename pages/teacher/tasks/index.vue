<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 自动导航栏 -->
    <AppNavigation />

    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">任务管理</h1>
        <p class="text-gray-600 mt-2">管理您的教学任务和学生进度</p>
      </div>
      <UButton @click="showCreateModal = true" color="primary">
        <UIcon name="i-heroicons-plus" class="mr-2" />
        创建任务
      </UButton>
    </div>

    <!-- 筛选器 -->
    <div class="mb-6">
      <UFormGroup label="按项目筛选">
        <USelect
          v-model="selectedProjectId"
          :options="projectFilterOptions"
          placeholder="选择项目（可选）"
          @change="filterTasks"
        />
      </UFormGroup>
    </div>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <UIcon name="i-heroicons-exclamation-triangle" class="text-4xl text-red-500 mb-4" />
      <p class="text-red-600 mb-4">加载任务列表时出现错误</p>
      <UButton @click="refresh()" variant="outline">重试</UButton>
    </div>

    <!-- 任务列表 -->
    <div v-else>
      <div v-if="!filteredTasks?.length" class="text-center py-12">
        <UIcon name="i-heroicons-document-text" class="text-6xl text-gray-300 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无任务</h3>
        <p class="text-gray-500 mb-4">开始创建您的第一个任务</p>
        <UButton @click="showCreateModal = true" color="primary">
          创建任务
        </UButton>
      </div>

      <div v-else class="space-y-4">
        <UCard 
          v-for="task in filteredTasks" 
          :key="task.task_id"
          class="hover:shadow-lg transition-shadow"
        >
          <div class="flex justify-between items-start">
            <div class="flex-1">
              <div class="flex items-center justify-between mb-2">
                <h3 class="text-lg font-semibold text-gray-900">
                  {{ task.title }}
                </h3>
                <UDropdown :items="getTaskActions(task)">
                  <UButton 
                    variant="ghost" 
                    size="sm" 
                    icon="i-heroicons-ellipsis-vertical"
                  />
                </UDropdown>
              </div>

              <div class="flex items-center text-sm text-gray-500 mb-2">
                <UIcon name="i-heroicons-folder" class="mr-1" />
                {{ task.project_name }}
              </div>

              <p v-if="task.description" class="text-gray-600 text-sm mb-3">
                {{ task.description }}
              </p>

              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4 text-sm">
                  <div class="flex items-center text-gray-500">
                    <UIcon name="i-heroicons-users" class="mr-1" />
                    {{ task.total_students }} 名学生
                  </div>
                  <div class="flex items-center text-blue-600">
                    <UIcon name="i-heroicons-paper-airplane" class="mr-1" />
                    {{ task.submitted_count }} 已提交
                  </div>
                  <div class="flex items-center text-green-600">
                    <UIcon name="i-heroicons-check-circle" class="mr-1" />
                    {{ task.completed_count }} 已完成
                  </div>
                  <div v-if="task.needs_revision_count > 0" class="flex items-center text-orange-600">
                    <UIcon name="i-heroicons-exclamation-circle" class="mr-1" />
                    {{ task.needs_revision_count }} 需修改
                  </div>
                </div>

                <div class="text-right">
                  <div v-if="task.due_date" class="text-sm text-gray-500">
                    截止日期: {{ formatDate(task.due_date) }}
                  </div>
                  <div class="text-xs text-gray-400">
                    创建于 {{ formatDate(task.created_at) }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </UCard>
      </div>
    </div>

    <!-- 创建任务模态框 -->
    <UModal v-model="showCreateModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">创建新任务</h3>
        </template>

        <form @submit.prevent="createTask" class="space-y-4">
          <UFormGroup label="选择项目" required :error="formErrors.project_id">
            <USelect
              v-model="createForm.project_id"
              :options="projectOptions"
              placeholder="请选择项目"
              :disabled="creating"
            />
          </UFormGroup>

          <UFormGroup label="任务标题" required :error="formErrors.title">
            <UInput
              v-model="createForm.title"
              placeholder="请输入任务标题"
              :disabled="creating"
            />
          </UFormGroup>

          <UFormGroup label="任务描述">
            <UTextarea
              v-model="createForm.description"
              placeholder="请输入任务描述（可选）"
              :disabled="creating"
              rows="3"
            />
          </UFormGroup>

          <UFormGroup label="截止日期">
            <UInput
              v-model="createForm.due_date"
              type="datetime-local"
              :disabled="creating"
            />
          </UFormGroup>

          <div class="flex justify-end space-x-3 pt-4">
            <UButton 
              variant="outline" 
              @click="showCreateModal = false"
              :disabled="creating"
            >
              取消
            </UButton>
            <UButton 
              type="submit" 
              color="primary"
              :loading="creating"
            >
              创建任务
            </UButton>
          </div>
        </form>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 响应式数据
const showCreateModal = ref(false)
const creating = ref(false)
const selectedProjectId = ref<number | null>(null)

// 创建表单
const createForm = ref({
  project_id: null as number | null,
  title: '',
  description: '',
  due_date: ''
})

// 获取任务列表
const { data: tasksResponse, pending, error, refresh } = await useAuthFetch('/api/teacher/tasks', {
  server: false,
  default: () => ({ success: true, data: [] })
})

// 获取项目列表
const { data: projectsResponse } = await useAuthFetch('/api/teacher/projects', {
  server: false,
  default: () => ({ success: true, data: [] })
})

// 提取实际数据
const tasks = computed(() => tasksResponse.value?.data || [])
const projects = computed(() => projectsResponse.value?.data || [])

// 项目选项（用于筛选）
const projectFilterOptions = computed(() => [
  { label: '全部项目', value: null },
  ...(projects.value?.map(project => ({
    label: project.project_name,
    value: project.project_id
  })) || [])
])

// 项目选项（用于创建任务）
const projectOptions = computed(() =>
  projects.value?.map(project => ({
    label: project.project_name,
    value: project.project_id
  })) || []
)

// 筛选后的任务
const filteredTasks = computed(() => {
  if (!selectedProjectId.value) {
    return tasks.value
  }
  return tasks.value?.filter(task => task.project_id === selectedProjectId.value)
})

// 筛选任务
const filterTasks = () => {
  // 筛选逻辑已在 computed 中处理
}

// 表单验证错误
const formErrors = ref<Record<string, string>>({})

// 验证表单
const validateForm = () => {
  formErrors.value = {}

  if (!createForm.value.project_id) {
    formErrors.value.project_id = '请选择项目'
  }

  if (!createForm.value.title.trim()) {
    formErrors.value.title = '任务标题不能为空'
  } else if (createForm.value.title.length > 200) {
    formErrors.value.title = '任务标题不能超过200个字符'
  }

  return Object.keys(formErrors.value).length === 0
}

// 创建任务
const createTask = async () => {
  // 验证表单
  if (!validateForm()) {
    return
  }

  creating.value = true
  try {
    const accessToken = useCookie('accessToken')
    const response = await $fetch('/api/teacher/tasks', {
      method: 'POST',
      body: createForm.value,
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      }
    })

    if (response.success) {
      // 重置表单
      createForm.value = {
        project_id: null,
        title: '',
        description: '',
        due_date: ''
      }
      formErrors.value = {}

      // 关闭模态框
      showCreateModal.value = false

      // 刷新任务列表
      await refresh()

      // 显示成功消息
      const toast = useToast()
      toast.add({
        title: '创建成功',
        description: '任务创建成功',
        color: 'green'
      })
    }
  } catch (error: any) {
    console.error('创建任务失败:', error)

    // 显示错误消息
    const toast = useToast()
    let errorMessage = '创建任务时发生错误'

    if (error.data?.error?.message) {
      errorMessage = error.data.error.message
    } else if (error.message) {
      errorMessage = error.message
    }

    toast.add({
      title: '创建失败',
      description: errorMessage,
      color: 'red'
    })
  } finally {
    creating.value = false
  }
}

// 获取任务操作菜单
const getTaskActions = (task: any) => [
  [{
    label: '查看详情',
    icon: 'i-heroicons-eye',
    click: () => viewTask(task.task_id)
  }],
  [{
    label: '编辑任务',
    icon: 'i-heroicons-pencil',
    click: () => editTask(task.task_id)
  }],
  [{
    label: '删除任务',
    icon: 'i-heroicons-trash',
    click: () => deleteTask(task.task_id)
  }]
]

// 查看任务详情
const viewTask = (taskId: number) => {
  navigateTo(`/teacher/tasks/${taskId}`)
}

// 编辑任务
const editTask = (taskId: number) => {
  navigateTo(`/teacher/tasks/${taskId}/edit`)
}

// 删除任务
const deleteTask = async (taskId: number) => {
  // TODO: 添加确认对话框
  try {
    const accessToken = useCookie('accessToken')
    await $fetch(`/api/teacher/tasks/${taskId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      }
    })

    // 刷新任务列表
    await refresh()

    // TODO: 显示成功消息
  } catch (error) {
    console.error('删除任务失败:', error)
    // TODO: 显示错误消息
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// Set page title
useHead({
  title: '任务管理 - 学生任务管理系统'
})
</script>
