<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <h1 class="text-3xl font-bold text-gray-900 mb-4">
          学生任务管理系统
        </h1>
        <div v-if="loading" class="text-gray-600">
          正在加载...
        </div>
        <div v-else class="space-y-4">
          <p class="text-gray-600">
            欢迎使用学生任务管理系统
          </p>
          <UButton @click="navigateTo('/login')" size="lg">
            立即登录
          </UButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Home page - redirect based on authentication status
const loading = ref(true)

onMounted(async () => {
  try {
    // Get auth store in the mounted hook
    const authStore = useAuthStore()

    // Check authentication status
    const isAuthenticated = await authStore.checkAuth()

    if (isAuthenticated && authStore.user) {
      const user = authStore.user
      // Redirect to appropriate dashboard
      if (user.role === 'teacher') {
        await navigateTo('/teacher/dashboard')
      } else if (user.role === 'student') {
        await navigateTo('/student/dashboard')
      } else if (user.role === 'admin') {
        await navigateTo('/admin/dashboard')
      }
    }
  } catch (error) {
    console.error('Authentication check failed:', error)
  } finally {
    loading.value = false
  }
})

// Set page title
useHead({
  title: '学生任务管理系统'
})
</script>