import { requireTeacher } from '../../../utils/auth-server'
import { TaskRepository } from '../../../repositories/task'
import { ProjectRepository } from '../../../repositories/project'
import { z } from 'zod'

// 创建任务请求验证
const createTaskSchema = z.object({
  project_id: z.union([z.number(), z.string()]).transform((val) => {
    const num = typeof val === 'string' ? parseInt(val, 10) : val
    if (isNaN(num) || num < 1) {
      throw new Error('请选择项目')
    }
    return num
  }),
  title: z.string().min(1, '任务标题不能为空').max(200, '任务标题不能超过200个字符'),
  description: z.string().optional().default(''),
  due_date: z.string().optional().nullable()
})

// 创建任务响应接口
interface CreateTaskResponse {
  success: boolean
  data?: {
    task_id: number
    title: string
    description: string | null
    due_date: string | null
    created_at: string
    project_id: number
  }
  error?: {
    code: string
    message: string
  }
}

export default defineEventHandler(async (event): Promise<CreateTaskResponse> => {
  try {
    // 验证教师权限
    const teacher = await requireTeacher(event)

    // 获取请求体
    const body = await readBody(event)

    // 验证请求数据
    const validation = createTaskSchema.safeParse(body)
    if (!validation.success) {
      console.error('Task creation validation error:', validation.error.errors)
      setResponseStatus(event, 400)

      // 获取第一个验证错误
      const firstError = validation.error.errors[0]
      let errorMessage = '请求数据格式错误'

      if (firstError) {
        if (firstError.path.includes('project_id')) {
          errorMessage = '请选择项目'
        } else if (firstError.path.includes('title')) {
          errorMessage = firstError.message
        } else {
          errorMessage = firstError.message
        }
      }

      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: errorMessage
        }
      }
    }

    const { project_id, title, description, due_date } = validation.data

    // 初始化仓储
    const taskRepository = new TaskRepository()
    const projectRepository = new ProjectRepository()

    // 验证项目是否存在且属于当前教师
    const project = await projectRepository.findById(project_id)
    if (!project) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'PROJECT_NOT_FOUND',
          message: '项目不存在'
        }
      }
    }

    if (project.teacher_id !== teacher.user_id) {
      setResponseStatus(event, 403)
      return {
        success: false,
        error: {
          code: 'ACCESS_DENIED',
          message: '无权在此项目中创建任务'
        }
      }
    }

    // 创建任务
    const task = await taskRepository.createTask({
      project_id,
      title,
      description,
      due_date: due_date ? new Date(due_date) : undefined
    })

    return {
      success: true,
      data: {
        task_id: task.task_id,
        title: task.title,
        description: task.description,
        due_date: task.due_date ? task.due_date.toISOString() : null,
        created_at: task.created_at.toISOString(),
        project_id: task.project_id
      }
    }
  } catch (error) {
    console.error('Create task API error:', error)
    
    setResponseStatus(event, 500)
    return {
      success: false,
      error: {
        code: 'INTERNAL_ERROR',
        message: '创建任务时发生错误'
      }
    }
  }
})
