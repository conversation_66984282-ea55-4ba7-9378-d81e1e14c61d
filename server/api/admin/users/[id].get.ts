// Get single user (admin only)
import { UserRepository } from '~/repositories/user'
import { requireAdmin } from '~/utils/auth-server'
import type { ApiError } from '~/types'

export default defineEventHandler(async (event) => {
  try {
    // Require admin authentication
    await requireAdmin(event)

    // Get user ID from route params
    const userId = parseInt(getRouterParam(event, 'id') || '0')
    if (!userId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '无效的用户ID'
        } as ApiError
      }
    }

    const userRepository = new UserRepository()

    // Get user (without password)
    const user = await userRepository.findByIdSafe(userId)
    if (!user) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: '用户不存在'
        } as ApiError
      }
    }

    return {
      success: true,
      data: {
        user
      }
    }
  } catch (error: any) {
    console.error('Failed to get user:', error)
    
    setResponseStatus(event, error.statusCode || 500)
    return {
      success: false,
      error: {
        code: error.statusCode === 403 ? 'FORBIDDEN' : 'INTERNAL_ERROR',
        message: error.statusCode === 403 ? '权限不足' : '获取用户信息失败'
      } as ApiError
    }
  }
})
