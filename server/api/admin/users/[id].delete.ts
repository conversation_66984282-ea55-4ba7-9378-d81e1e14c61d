// Delete user (admin only)
import { UserRepository } from '~/repositories/user'
import { requireAdmin } from '~/utils/auth-server'
import type { ApiError } from '~/types'

export default defineEventHandler(async (event) => {
  try {
    // Require admin authentication
    const currentUser = await requireAdmin(event)

    // Get user ID from route params
    const userId = parseInt(getRouterParam(event, 'id') || '0')
    if (!userId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '无效的用户ID'
        } as ApiError
      }
    }

    // Prevent admin from deleting themselves
    if (currentUser.user_id === userId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '不能删除自己的账号'
        } as ApiError
      }
    }

    const userRepository = new UserRepository()

    // Check if user exists
    const existingUser = await userRepository.findById(userId)
    if (!existingUser) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: '用户不存在'
        } as ApiError
      }
    }

    // Delete user
    const deleted = await userRepository.delete(userId)
    if (!deleted) {
      setResponseStatus(event, 500)
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '删除用户失败'
        } as ApiError
      }
    }

    return {
      success: true,
      data: {
        message: '用户删除成功'
      }
    }
  } catch (error: any) {
    console.error('Failed to delete user:', error)
    
    let statusCode = 500
    let errorCode = 'INTERNAL_ERROR'
    let message = '删除用户失败'

    if (error.statusCode === 403) {
      statusCode = 403
      errorCode = 'FORBIDDEN'
      message = '权限不足'
    }

    setResponseStatus(event, statusCode)
    return {
      success: false,
      error: {
        code: errorCode,
        message
      } as ApiError
    }
  }
})
