// Update user (admin only)
import { UserRepository, type UpdateUserData } from '~/repositories/user'
import { requireAdmin } from '~/utils/auth-server'
import { validateUpdateUser } from '~/utils/validation'
import type { ApiError } from '~/types'

export default defineEventHandler(async (event) => {
  try {
    // Require admin authentication
    await requireAdmin(event)

    // Get user ID from route params
    const userId = parseInt(getRouterParam(event, 'id') || '0')
    if (!userId) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '无效的用户ID'
        } as ApiError
      }
    }

    // Get request body
    const body = await readBody(event)

    // Validate input
    const validation = validateUpdateUser(body)
    if (!validation.success) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '输入数据无效',
          details: validation.errors
        } as ApiError
      }
    }

    const updateData: UpdateUserData = validation.data

    const userRepository = new UserRepository()

    // Check if user exists
    const existingUser = await userRepository.findById(userId)
    if (!existingUser) {
      setResponseStatus(event, 404)
      return {
        success: false,
        error: {
          code: 'NOT_FOUND',
          message: '用户不存在'
        } as ApiError
      }
    }

    // Update user
    const updatedUser = await userRepository.updateUser(userId, updateData)
    if (!updatedUser) {
      setResponseStatus(event, 500)
      return {
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: '更新用户失败'
        } as ApiError
      }
    }

    // Return user without password
    const { password_hash, ...safeUser } = updatedUser

    return {
      success: true,
      data: {
        user: safeUser
      }
    }
  } catch (error: any) {
    console.error('Failed to update user:', error)
    
    let statusCode = 500
    let errorCode = 'INTERNAL_ERROR'
    let message = '更新用户失败'

    if (error.statusCode === 403) {
      statusCode = 403
      errorCode = 'FORBIDDEN'
      message = '权限不足'
    } else if (error.message.includes('已存在')) {
      statusCode = 409
      errorCode = 'CONFLICT'
      message = error.message
    }

    setResponseStatus(event, statusCode)
    return {
      success: false,
      error: {
        code: errorCode,
        message
      } as ApiError
    }
  }
})
