// Get all users (admin only)
import { UserRepository } from '~/repositories/user'
import { requireAdmin } from '~/utils/auth-server'
import type { ApiError } from '~/types'

export default defineEventHandler(async (event) => {
  try {
    // Require admin authentication
    await requireAdmin(event)

    // Get query parameters
    const query = getQuery(event)
    const role = query.role as 'teacher' | 'student' | 'admin' | undefined
    const search = query.search as string | undefined
    const page = parseInt(query.page as string) || 1
    const limit = parseInt(query.limit as string) || 20

    const userRepository = new UserRepository()
    let users

    if (search) {
      // Search users
      users = await userRepository.searchUsers(search, role)
    } else {
      // Get all users by role
      users = await userRepository.findAllSafe(role)
    }

    // Simple pagination
    const startIndex = (page - 1) * limit
    const endIndex = startIndex + limit
    const paginatedUsers = users.slice(startIndex, endIndex)

    return {
      success: true,
      data: {
        users: paginatedUsers,
        total: users.length,
        page,
        limit,
        totalPages: Math.ceil(users.length / limit)
      }
    }
  } catch (error: any) {
    console.error('Failed to get users:', error)
    
    setResponseStatus(event, error.statusCode || 500)
    return {
      success: false,
      error: {
        code: error.statusCode === 403 ? 'FORBIDDEN' : 'INTERNAL_ERROR',
        message: error.statusCode === 403 ? '权限不足' : '获取用户列表失败'
      } as ApiError
    }
  }
})
