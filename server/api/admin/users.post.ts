// Create new user (admin only)
import { UserRepository, type CreateUserData } from '~/repositories/user'
import { requireAdmin } from '~/utils/auth-server'
import { validateCreateUser } from '~/utils/validation'
import type { ApiError } from '~/types'

export default defineEventHandler(async (event) => {
  try {
    // Require admin authentication
    await requireAdmin(event)

    // Get request body
    const body = await readBody(event)

    // Validate input
    const validation = validateCreateUser(body)
    if (!validation.success) {
      setResponseStatus(event, 400)
      return {
        success: false,
        error: {
          code: 'VALIDATION_ERROR',
          message: '输入数据无效',
          details: validation.errors
        } as ApiError
      }
    }

    const userData: CreateUserData = validation.data

    const userRepository = new UserRepository()

    // Create user
    const newUser = await userRepository.createUser(userData)

    // Return user without password
    const { password_hash, ...safeUser } = newUser

    return {
      success: true,
      data: {
        user: safeUser
      }
    }
  } catch (error: any) {
    console.error('Failed to create user:', error)
    
    let statusCode = 500
    let errorCode = 'INTERNAL_ERROR'
    let message = '创建用户失败'

    if (error.statusCode === 403) {
      statusCode = 403
      errorCode = 'FORBIDDEN'
      message = '权限不足'
    } else if (error.message.includes('已存在')) {
      statusCode = 409
      errorCode = 'CONFLICT'
      message = error.message
    }

    setResponseStatus(event, statusCode)
    return {
      success: false,
      error: {
        code: errorCode,
        message
      } as ApiError
    }
  }
})
