import { UserRepository, type CreateUserData } from '../repositories/user'
import { pool } from '../config/database'

// 管理员用户数据
const adminUser: CreateUserData = {
  username: 'admin',
  password: 'admin123456',
  email: '<EMAIL>',
  full_name: '系统管理员',
  role: 'admin'
}

async function createAdminUser() {
  console.log('🚀 开始创建管理员用户...')
  
  try {
    const userRepository = new UserRepository()
    
    // 检查是否已有管理员用户
    const existingAdmins = await userRepository.findByRole('admin')
    if (existingAdmins.length > 0) {
      console.log('⚠️  系统中已存在管理员用户:')
      existingAdmins.forEach(admin => {
        console.log(`   用户名: ${admin.username} | 姓名: ${admin.full_name} | 邮箱: ${admin.email}`)
      })
      console.log('\n如果需要创建新的管理员用户，请修改脚本中的用户名和邮箱。')
      return
    }
    
    // 检查用户名是否已存在
    const existingUser = await userRepository.findByUsername(adminUser.username)
    if (existingUser) {
      console.log(`❌ 用户名 "${adminUser.username}" 已存在`)
      console.log('请修改脚本中的用户名后重试。')
      return
    }
    
    // 检查邮箱是否已存在
    const existingEmail = await userRepository.findByEmail(adminUser.email)
    if (existingEmail) {
      console.log(`❌ 邮箱 "${adminUser.email}" 已存在`)
      console.log('请修改脚本中的邮箱后重试。')
      return
    }
    
    console.log('📝 创建管理员用户中...')
    
    // 创建管理员用户
    const createdAdmin = await userRepository.createUser(adminUser)
    
    console.log('✅ 管理员用户创建成功！')
    console.log('\n📊 管理员用户信息:')
    console.log('=' .repeat(60))
    console.log(`   用户ID: ${createdAdmin.user_id}`)
    console.log(`   用户名: ${createdAdmin.username}`)
    console.log(`   姓名: ${createdAdmin.full_name}`)
    console.log(`   邮箱: ${createdAdmin.email}`)
    console.log(`   角色: ${createdAdmin.role}`)
    console.log(`   创建时间: ${createdAdmin.created_at}`)
    
    console.log('\n🔑 登录信息:')
    console.log(`   用户名: ${adminUser.username}`)
    console.log(`   密码: ${adminUser.password}`)
    
    console.log('\n💡 使用说明:')
    console.log('   1. 访问 http://localhost:3000/login')
    console.log('   2. 使用上述用户名和密码登录')
    console.log('   3. 管理员可以访问用户管理界面')
    console.log('   4. 建议登录后立即修改默认密码')
    
  } catch (error: any) {
    console.error('❌ 创建管理员用户失败:', error.message)
    
    if (error.message.includes('已存在')) {
      console.log('\n💡 解决方案:')
      console.log('   1. 检查数据库中是否已有相同用户名或邮箱的用户')
      console.log('   2. 修改脚本中的用户名和邮箱')
      console.log('   3. 重新运行此脚本')
    }
  } finally {
    // 关闭数据库连接
    await pool.end()
  }
}

// 运行脚本
createAdminUser().catch(console.error)
